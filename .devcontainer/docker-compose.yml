services:
  sentry-build: &sentry-build
    build:
      context: ..
      dockerfile: .devcontainer/Dockerfile
      target: dev
      args:
        IMAGE: ${IMAGE}
        VERSION: ${VERSION}
        SENTRY_E2E_RAILS_APP_PORT: ${SENTRY_E2E_RAILS_APP_PORT}
        SENTRY_E2E_SVELTE_APP_PORT: ${SENTRY_E2E_SVELTE_APP_PORT}
        SENTRY_E2E_RAILS_APP_URL: ${SENTRY_E2E_RAILS_APP_URL}
        SENTRY_E2E_SVELTE_APP_URL: ${SENTRY_E2E_SVELTE_APP_URL}
    volumes:
      - ..:/workspace/sentry:cached
      - sentry_e2e_logs:/workspace/sentry/log
      - sentry_ruby_gems:/workspace/sentry/vendor/gems
      - sentry_node_modules:/workspace/sentry/spec/apps/svelte-mini/node_modules
    working_dir: /workspace/sentry
    environment:
      - REDIS_URL=${REDIS_URL:-redis://redis:6379/0}
    env_file: [".env"]

  sentry-dev:
    <<: *sentry-build
    command: "sh -c '.devcontainer/setup --with-foreman --only-bundle'"
    depends_on:
      - redis

  sentry-test:
    <<: *sentry-build
    entrypoint: "sh -c '.devcontainer/setup --with-foreman --only-bundle --only .'"
    depends_on:
      - redis
      - sentry-test-services

  sentry-test-services:
    <<: *sentry-build
    command: "sh -c '.devcontainer/setup --with-foreman --only .,spec/apps/rails-mini && foreman start'"
    ports:
      - ${SENTRY_E2E_RAILS_APP_PORT}
      - ${SENTRY_E2E_SVELTE_APP_PORT}

  redis:
    image: redis:latest
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
    ports:
      - "6379:6379"

volumes:
  sentry_e2e_logs:
  sentry_ruby_gems:
  sentry_node_modules:
