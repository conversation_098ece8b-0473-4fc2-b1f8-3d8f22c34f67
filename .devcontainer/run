#!/bin/bash

set -e

# Fix ownership of mounted volumes
sudo chown -R sentry:sentry /workspace/sentry/vendor/gems
sudo chown -R sentry:sentry /workspace/sentry/log
sudo chown -R sentry:sentry /workspace/sentry/spec/apps/svelte-mini/node_modules

git config --global --add safe.directory /workspace/sentry

# Function to run setup for a specific service
run_service_setup() {
    local service="$1"
    
    echo "🚀 Running setup for service: $service"
    
    case "$service" in
        "dev")
            .devcontainer/setup --with-foreman --only-bundle
            ;;
        "test")
            .devcontainer/setup --with-foreman --only-bundle --only .
            ;;
        "test-services")
            .devcontainer/setup --with-foreman --only .,spec/apps/rails-mini
            ;;
        *)
            echo "❌ Unknown service: $service"
            echo "Available services: dev, test, test-services"
            exit 1
            ;;
    esac
    
    echo "✅ Setup completed for service: $service"
}

# Parse arguments
if [ "$1" = "--service" ] && [ -n "$2" ]; then
    service="$2"
    shift 2  # Remove --service and service name from arguments
    
    # Run service-specific setup
    run_service_setup "$service"
    
    # Execute the remaining command
    if [ $# -gt 0 ]; then
        exec "$@"
    else
        # If no command provided, start an interactive shell
        exec bash
    fi
else
    echo "❌ Usage: $0 --service <service-name> [command...]"
    echo "Available services: dev, test, test-services"
    exit 1
fi
