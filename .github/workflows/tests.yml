name: Tests & CodeCov

on:
  workflow_dispatch:
  # push:
  #   branches:
  #     - master
  #     - \d+-\d+
  # pull_request:

concurrency:
  group: tests-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  ruby-versions:
    uses: ruby/actions/.github/workflows/ruby_versions.yml@6d15c16f6259d657961bcdccf2598d3d53e90635
    with:
      engine: cruby-jruby
      min_version: 2.7
      versions: '["jruby-9.4.12.0"]'
    secrets: inherit

  delayed_job-tests:
    needs: ruby-versions
    uses: ./.github/workflows/sentry_delayed_job_test.yml
    with:
      versions: ${{ needs.ruby-versions.outputs.versions }}
    secrets: inherit

  opentelemetry-tests:
    needs: ruby-versions
    uses: ./.github/workflows/sentry_opentelemetry_test.yml
    with:
      versions: ${{ needs.ruby-versions.outputs.versions }}
    secrets: inherit

  rails-tests:
    needs: ruby-versions
    uses: ./.github/workflows/sentry_rails_test.yml
    with:
      versions: ${{ needs.ruby-versions.outputs.versions }}
    secrets: inherit

  resque-tests:
    needs: ruby-versions
    uses: ./.github/workflows/sentry_resque_test.yml
    with:
      versions: ${{ needs.ruby-versions.outputs.versions }}
    secrets: inherit

  ruby-tests:
    needs: ruby-versions
    uses: ./.github/workflows/sentry_ruby_test.yml
    with:
      versions: ${{ needs.ruby-versions.outputs.versions }}
    secrets: inherit

  sidekiq-tests:
    needs: ruby-versions
    uses: ./.github/workflows/sentry_sidekiq_test.yml
    with:
      versions: ${{ needs.ruby-versions.outputs.versions }}
    secrets: inherit

  codecov:
    name: CodeCov
    runs-on: ubuntu-latest
    needs:
      - ruby-tests
      - rails-tests
      - sidekiq-tests
      - delayed_job-tests
      - resque-tests
      - opentelemetry-tests
    steps:
      - uses: actions/checkout@v4

      - name: Codecov
        uses: codecov/codecov-action@13ce06bfc6bbe3ecf90edbbf1bc32fe5978ca1d3
        with:
          run_command: send-notifications
          token: ${{ secrets.CODECOV_TOKEN }}
