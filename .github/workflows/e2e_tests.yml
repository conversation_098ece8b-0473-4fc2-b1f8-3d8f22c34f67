name: e2e tests

on:
  workflow_dispatch:
  push:
    branches:
      - master
  pull_request:

concurrency:
  group: e2e-tests-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  e2e-tests:
    name: e2e tests
    runs-on: ubuntu-latest
    timeout-minutes: 5

    strategy:
      fail-fast: false
      matrix:
        ruby_version: ["3.4.5"]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up `.env` file
        run: |
          cd .devcontainer
          cp .env.example .env

      - name: Restore rubygems cache
        uses: actions/cache@v3
        with:
          path: vendor/gems/${{ matrix.ruby_version }}
          key: ${{ runner.os }}-${{ matrix.ruby_version }}-gems-${{ hashFiles('Gemfile.lock', '*/Gemfile.lock', 'spec/apps/**/Gemfile.lock') }}
          restore-keys: ${{ runner.os }}-platform-node-modules-

      - name: Restore node_modules cache
        uses: actions/cache@v3
        with:
          path: spec/apps/svelte-mini/node_modules
          key: ${{ runner.os }}-node-modules-${{ hashFiles('spec/apps/svelte-mini/package-lock.json') }}
          restore-keys: ${{ runner.os }}-platform-node-modules-

      - name: Build sentry-test-services
        run: |
          docker compose \
            --file .devcontainer/docker-compose.yml \
            --env-file .devcontainer/.env \
            build sentry-test-services

      - name: Start test services
        run: |
          docker compose \
            --file .devcontainer/docker-compose.yml \
            --env-file .devcontainer/.env \
            up -d sentry-test-services

      - name: Build sentry-test
        run: |
          docker compose \
            --file .devcontainer/docker-compose.yml \
            --env-file .devcontainer/.env \
            build sentry-test

      - name: Run e2e tests via sentry-test
        run: |
          docker compose \
            --file .devcontainer/docker-compose.yml \
            --env-file .devcontainer/.env \
            run --rm sentry-test \
            bundle exec rake

      - name: Stop e2e services
        if: always()
        run: |
          cd .devcontainer
          source .env
          docker compose --profile e2e down

      - name: Upload test artifacts
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: e2e-test-logs-ruby-${{ matrix.ruby_version }}
          path: |
            log/sentry_debug_events.log
          retention-days: 7

      - # Temp fix
        # https://github.com/docker/build-push-action/issues/252
        # https://github.com/moby/buildkit/issues/1896
        name: Move cache
        run: |
          rm -rf ${{ runner.temp }}/.buildx-cache
          mv ${{ runner.temp }}/.buildx-cache-new ${{ runner.temp }}/.buildx-cache
